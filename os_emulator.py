import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
import subprocess
import time
import threading
from datetime import datetime
import json
import webbrowser
import requests
import random
from tkinter import font

class FastOS:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("FastOS - Nejrychlejší OS na světě! 🚀")
        self.root.geometry("1200x800")
        self.root.configure(bg='#1a1a2e')
        
        # Systémové proměnné
        self.running_apps = []
        self.system_time = datetime.now()
        self.cpu_usage = 0
        self.memory_usage = 0
        
        # Vytvoření hlavního rozhraní
        self.create_desktop()
        self.create_taskbar()
        self.create_start_menu()
        
        # Spuštění systémových procesů
        self.start_system_processes()
        
    def create_desktop(self):
        """Vytvoří desktop s pozadím"""
        self.desktop_frame = tk.Frame(self.root, bg='#0f3460')
        self.desktop_frame.pack(fill='both', expand=True)
        
        # Pozadí s logem
        self.logo_label = tk.Label(
            self.desktop_frame, 
            text="FastOS\n🚀", 
            font=('Arial', 48, 'bold'),
            fg='#e94560',
            bg='#0f3460'
        )
        self.logo_label.place(relx=0.5, rely=0.5, anchor='center')
        
        # Desktop ikony
        self.create_desktop_icons()

        # Přidání integrovaných widgetů
        self.create_integrated_widgets()
        
    def create_desktop_icons(self):
        """Vytvoří ikony na ploše"""
        icons = [
            ("📁 Soubory", self.open_file_manager, 50, 50),
            ("⚙️ Nastavení", self.open_settings, 50, 150),
            ("🎮 Hry", self.open_games, 50, 250),
            ("💻 Terminál", self.open_terminal, 50, 350),
            ("🌐 Prohlížeč", self.open_browser, 50, 450),
            ("📊 Správce úloh", self.open_task_manager, 150, 50),
            ("🎨 AI Malíř", self.open_ai_painter, 150, 150),
            ("🤖 ChatBot", self.open_chatbot, 150, 250),
            ("🎵 Hudba", self.open_music_center, 150, 350),
            ("🌟 Matrix", self.open_matrix_effect, 250, 50),
        ]
        
        for text, command, x, y in icons:
            btn = tk.Button(
                self.desktop_frame,
                text=text,
                command=command,
                font=('Arial', 10, 'bold'),
                bg='#16213e',
                fg='white',
                relief='flat',
                width=12,
                height=2
            )
            btn.place(x=x, y=y)
            
    def create_taskbar(self):
        """Vytvoří spodní lištu"""
        self.taskbar = tk.Frame(self.root, bg='#16213e', height=50)
        self.taskbar.pack(side='bottom', fill='x')
        self.taskbar.pack_propagate(False)
        
        # Start tlačítko
        self.start_btn = tk.Button(
            self.taskbar,
            text="🚀 START",
            command=self.toggle_start_menu,
            bg='#e94560',
            fg='white',
            font=('Arial', 12, 'bold'),
            relief='flat'
        )
        self.start_btn.pack(side='left', padx=5, pady=5)
        
        # Systémové informace
        self.system_info = tk.Label(
            self.taskbar,
            text="CPU: 0% | RAM: 0% | Čas: 00:00",
            bg='#16213e',
            fg='white',
            font=('Arial', 10)
        )
        self.system_info.pack(side='right', padx=10, pady=5)
        
    def create_start_menu(self):
        """Vytvoří start menu"""
        self.start_menu = tk.Toplevel(self.root)
        self.start_menu.title("Start Menu")
        self.start_menu.geometry("300x400")
        self.start_menu.configure(bg='#16213e')
        self.start_menu.withdraw()  # Skryje menu na začátku
        
        # Menu položky
        menu_items = [
            ("📁 Správce souborů", self.open_file_manager),
            ("💻 Terminál", self.open_terminal),
            ("🎮 Herní centrum", self.open_games),
            ("🌐 Web Browser", self.open_browser),
            ("⚙️ Nastavení", self.open_settings),
            ("📊 Správce úloh", self.open_task_manager),
            ("🔧 Systémové nástroje", self.open_system_tools),
            ("❌ Vypnout", self.shutdown_system)
        ]
        
        for text, command in menu_items:
            btn = tk.Button(
                self.start_menu,
                text=text,
                command=command,
                bg='#0f3460',
                fg='white',
                font=('Arial', 12),
                relief='flat',
                anchor='w',
                width=25
            )
            btn.pack(fill='x', padx=5, pady=2)
            
    def toggle_start_menu(self):
        """Zobrazí/skryje start menu"""
        if self.start_menu.winfo_viewable():
            self.start_menu.withdraw()
        else:
            # Umístí menu nad start tlačítko
            x = self.root.winfo_x() + 10
            y = self.root.winfo_y() + self.root.winfo_height() - 450
            self.start_menu.geometry(f"300x400+{x}+{y}")
            self.start_menu.deiconify()
            
    def start_system_processes(self):
        """Spustí systémové procesy"""
        self.update_system_info()
        
    def update_system_info(self):
        """Aktualizuje systémové informace"""
        try:
            import psutil
            cpu = psutil.cpu_percent()
            memory = psutil.virtual_memory().percent
            current_time = datetime.now().strftime("%H:%M")
            
            self.system_info.config(
                text=f"CPU: {cpu:.1f}% | RAM: {memory:.1f}% | Čas: {current_time}"
            )
        except:
            # Fallback pokud psutil není dostupný
            current_time = datetime.now().strftime("%H:%M")
            self.system_info.config(text=f"Čas: {current_time}")
            
        # Aktualizace každou sekundu
        self.root.after(1000, self.update_system_info)

    # ========== INTEGROVANÉ WIDGETY ==========

    def create_integrated_widgets(self):
        """Vytvoří integrované widgety přímo v OS"""
        # Pravý panel pro widgety
        self.right_panel = tk.Frame(self.desktop_frame, bg='#16213e', width=300)
        self.right_panel.pack(side='right', fill='y', padx=5, pady=5)
        self.right_panel.pack_propagate(False)

        # Widget počasí
        self.create_weather_panel()

        # Rychlé poznámky
        self.create_notes_panel()

        # Mini kalkulačka
        self.create_calc_panel()

        # Hudební přehrávač
        self.create_music_panel()

        # ChatBot panel
        self.create_chatbot_panel()

    def create_weather_panel(self):
        """Vytvoří panel počasí"""
        weather_frame = tk.LabelFrame(self.right_panel, text="🌤️ POČASÍ",
                                     bg='#16213e', fg='#e94560', font=('Arial', 12, 'bold'))
        weather_frame.pack(fill='x', padx=5, pady=5)

        self.weather_city = tk.Label(weather_frame, text="Praha",
                                    font=('Arial', 12, 'bold'), fg='white', bg='#16213e')
        self.weather_city.pack(pady=2)

        self.weather_temp = tk.Label(weather_frame, text="☀️ 23°C",
                                    font=('Arial', 16, 'bold'), fg='#ffaa00', bg='#16213e')
        self.weather_temp.pack(pady=2)

        self.weather_desc = tk.Label(weather_frame, text="Slunečno",
                                    font=('Arial', 10), fg='#cccccc', bg='#16213e')
        self.weather_desc.pack(pady=2)

        # Aktualizace počasí
        self.update_weather_panel()

    def update_weather_panel(self):
        """Aktualizuje panel počasí"""
        weathers = [
            ("☀️", "Slunečno", "#ffaa00"),
            ("⛅", "Polojasno", "#cccccc"),
            ("🌧️", "Déšť", "#4488ff"),
            ("❄️", "Sníh", "#ffffff"),
            ("⛈️", "Bouřka", "#ff4444")
        ]

        weather = random.choice(weathers)
        temp = random.randint(15, 30)

        self.weather_temp.config(text=f"{weather[0]} {temp}°C", fg=weather[2])
        self.weather_desc.config(text=weather[1])

        # Aktualizace každých 30 sekund
        self.root.after(30000, self.update_weather_panel)

    def create_notes_panel(self):
        """Vytvoří panel poznámek"""
        notes_frame = tk.LabelFrame(self.right_panel, text="📝 POZNÁMKY",
                                   bg='#16213e', fg='#e94560', font=('Arial', 12, 'bold'))
        notes_frame.pack(fill='x', padx=5, pady=5)

        self.notes_text = tk.Text(notes_frame, bg='#0f3460', fg='white',
                                 font=('Arial', 9), height=6, width=30)
        self.notes_text.pack(padx=5, pady=5)
        self.notes_text.insert(tk.END, "• Light or Dead optimalizace\n• Nové funkce FastOS\n• Nápady na vylepšení...")

    def create_calc_panel(self):
        """Vytvoří panel kalkulačky"""
        calc_frame = tk.LabelFrame(self.right_panel, text="🧮 KALKULAČKA",
                                  bg='#16213e', fg='#e94560', font=('Arial', 12, 'bold'))
        calc_frame.pack(fill='x', padx=5, pady=5)

        self.calc_display = tk.Entry(calc_frame, font=('Arial', 14), justify='right',
                                    bg='#0f3460', fg='white', width=20)
        self.calc_display.pack(pady=5, padx=5)
        self.calc_display.insert(0, "0")

        # Kompaktní tlačítka
        calc_buttons = [
            ['C', '÷', '×', '⌫'],
            ['7', '8', '9', '-'],
            ['4', '5', '6', '+'],
            ['1', '2', '3', '='],
            ['0', '.']
        ]

        for row in calc_buttons:
            button_frame = tk.Frame(calc_frame, bg='#16213e')
            button_frame.pack()
            for btn_text in row:
                if btn_text == '0':
                    btn = tk.Button(button_frame, text=btn_text, width=6, height=1,
                                   bg='#0f3460', fg='white', font=('Arial', 10),
                                   command=lambda t=btn_text: self.calc_button_click(t))
                else:
                    btn = tk.Button(button_frame, text=btn_text, width=2, height=1,
                                   bg='#0f3460', fg='white', font=('Arial', 10),
                                   command=lambda t=btn_text: self.calc_button_click(t))
                btn.pack(side='left', padx=1, pady=1)

    def calc_button_click(self, button):
        """Zpracuje kliknutí na kalkulačku"""
        current = self.calc_display.get()

        if button == 'C':
            self.calc_display.delete(0, tk.END)
            self.calc_display.insert(0, "0")
        elif button == '⌫':
            if len(current) > 1:
                self.calc_display.delete(len(current)-1)
            else:
                self.calc_display.delete(0, tk.END)
                self.calc_display.insert(0, "0")
        elif button == '=':
            try:
                # Nahradí symboly pro eval
                expression = current.replace('×', '*').replace('÷', '/')
                result = eval(expression)
                self.calc_display.delete(0, tk.END)
                self.calc_display.insert(0, str(result))
            except:
                self.calc_display.delete(0, tk.END)
                self.calc_display.insert(0, "Chyba")
        else:
            if current == "0" or current == "Chyba":
                self.calc_display.delete(0, tk.END)
                self.calc_display.insert(0, button)
            else:
                self.calc_display.insert(tk.END, button)

    def create_music_panel(self):
        """Vytvoří panel hudby"""
        music_frame = tk.LabelFrame(self.right_panel, text="🎵 HUDBA",
                                   bg='#16213e', fg='#e94560', font=('Arial', 12, 'bold'))
        music_frame.pack(fill='x', padx=5, pady=5)

        self.current_song = tk.Label(music_frame, text="🎵 FastOS Theme",
                                    font=('Arial', 10, 'bold'), fg='white', bg='#16213e')
        self.current_song.pack(pady=2)

        # Ovládání
        controls_frame = tk.Frame(music_frame, bg='#16213e')
        controls_frame.pack(pady=5)

        tk.Button(controls_frame, text="⏮️", bg='#0f3460', fg='white',
                 font=('Arial', 12), width=3, command=self.music_prev).pack(side='left', padx=2)
        tk.Button(controls_frame, text="⏸️", bg='#e94560', fg='white',
                 font=('Arial', 12), width=3, command=self.music_pause).pack(side='left', padx=2)
        tk.Button(controls_frame, text="⏭️", bg='#0f3460', fg='white',
                 font=('Arial', 12), width=3, command=self.music_next).pack(side='left', padx=2)

        # Progress bar
        self.music_progress = tk.Scale(music_frame, from_=0, to=100, orient='horizontal',
                                      bg='#16213e', fg='white', highlightthickness=0,
                                      length=250, showvalue=False)
        self.music_progress.pack(pady=2)
        self.music_progress.set(45)

    def music_prev(self):
        """Předchozí skladba"""
        songs = ["🎵 FastOS Theme", "🎮 Light or Dead OST", "⚡ Speed Boost", "🚀 System Startup"]
        current = self.current_song.cget("text")
        try:
            index = songs.index(current)
            new_song = songs[index - 1]
        except:
            new_song = songs[0]
        self.current_song.config(text=new_song)
        self.music_progress.set(0)

    def music_next(self):
        """Další skladba"""
        songs = ["🎵 FastOS Theme", "🎮 Light or Dead OST", "⚡ Speed Boost", "🚀 System Startup"]
        current = self.current_song.cget("text")
        try:
            index = songs.index(current)
            new_song = songs[(index + 1) % len(songs)]
        except:
            new_song = songs[0]
        self.current_song.config(text=new_song)
        self.music_progress.set(0)

    def music_pause(self):
        """Pauza/přehrávání"""
        # Simulace pauzy
        pass

    def create_chatbot_panel(self):
        """Vytvoří panel chatbota"""
        chat_frame = tk.LabelFrame(self.right_panel, text="🤖 AI ASISTENT",
                                  bg='#16213e', fg='#e94560', font=('Arial', 12, 'bold'))
        chat_frame.pack(fill='both', expand=True, padx=5, pady=5)

        self.mini_chat = tk.Text(chat_frame, bg='#0f3460', fg='white',
                                font=('Arial', 9), height=8, width=30, wrap='word')
        self.mini_chat.pack(padx=5, pady=5)
        self.mini_chat.insert(tk.END, "🤖 Ahoj! Jsem FastOS AI!\nNapiš 'help' pro nápovědu.")

        # Input
        chat_input_frame = tk.Frame(chat_frame, bg='#16213e')
        chat_input_frame.pack(fill='x', padx=5, pady=5)

        self.mini_chat_input = tk.Entry(chat_input_frame, bg='#0f3460', fg='white',
                                       font=('Arial', 9), width=20)
        self.mini_chat_input.pack(side='left', fill='x', expand=True)
        self.mini_chat_input.bind('<Return>', self.send_mini_chat)

        tk.Button(chat_input_frame, text="📤", bg='#e94560', fg='white',
                 font=('Arial', 9), command=self.send_mini_chat).pack(side='right', padx=(2,0))

    def send_mini_chat(self, event=None):
        """Odešle zprávu do mini chatu"""
        message = self.mini_chat_input.get().strip()
        if not message:
            return

        self.mini_chat.insert(tk.END, f"\n👤 {message}")
        self.mini_chat_input.delete(0, tk.END)

        # AI odpověď
        responses = {
            "help": "🤖 Příkazy: weather, time, calc, music, light",
            "weather": f"🌤️ {self.weather_desc.cget('text')}, {self.weather_temp.cget('text')}",
            "time": f"🕐 {datetime.now().strftime('%H:%M')}",
            "light": "🎮 Light or Dead běží skvěle na FastOS!",
            "music": f"🎵 Hraje: {self.current_song.cget('text')}",
            "calc": "🧮 Kalkulačka je připravena k použití!",
            "default": "🤖 Zajímavé! Zkus 'help' pro příkazy."
        }

        response = responses.get("default")
        for key in responses:
            if key in message.lower():
                response = responses[key]
                break

        self.mini_chat.insert(tk.END, f"\n🤖 {response}")
        self.mini_chat.see(tk.END)

    # ========== APLIKACE ==========

    def open_file_manager(self):
        """Otevře správce souborů"""
        file_window = tk.Toplevel(self.root)
        file_window.title("📁 FastOS Správce souborů")
        file_window.geometry("800x600")
        file_window.configure(bg='#1a1a2e')

        # Toolbar
        toolbar = tk.Frame(file_window, bg='#16213e', height=40)
        toolbar.pack(fill='x')
        toolbar.pack_propagate(False)

        tk.Button(toolbar, text="⬅️ Zpět", bg='#0f3460', fg='white').pack(side='left', padx=5, pady=5)
        tk.Button(toolbar, text="🏠 Domů", bg='#0f3460', fg='white').pack(side='left', padx=5, pady=5)
        tk.Button(toolbar, text="📁 Nová složka", bg='#0f3460', fg='white').pack(side='left', padx=5, pady=5)

        # Hlavní oblast
        main_frame = tk.Frame(file_window, bg='#1a1a2e')
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # Seznam souborů
        file_list = tk.Listbox(main_frame, bg='#0f3460', fg='white', font=('Arial', 12))
        file_list.pack(fill='both', expand=True)

        # Přidání ukázkových souborů
        sample_files = [
            "📁 Dokumenty",
            "📁 Obrázky",
            "📁 Hudba",
            "📁 Videa",
            "📄 readme.txt",
            "🎮 Light or Dead.exe",
            "⚙️ config.json"
        ]

        for file in sample_files:
            file_list.insert(tk.END, file)

    def open_terminal(self):
        """Otevře terminál"""
        terminal_window = tk.Toplevel(self.root)
        terminal_window.title("💻 FastOS Terminál")
        terminal_window.geometry("800x500")
        terminal_window.configure(bg='#000000')

        # Terminálový text
        self.terminal_text = tk.Text(
            terminal_window,
            bg='#000000',
            fg='#00ff00',
            font=('Courier', 12),
            insertbackground='#00ff00'
        )
        self.terminal_text.pack(fill='both', expand=True, padx=5, pady=5)

        # Úvodní text
        welcome_text = """FastOS Terminal v1.0
Copyright (c) 2024 FastOS Corporation
Nejrychlejší terminál na světě! 🚀

Dostupné příkazy:
- help: zobrazí nápovědu
- ls: vypíše soubory
- clear: vyčistí obrazovku
- date: zobrazí datum a čas
- sysinfo: systémové informace
- games: spustí herní centrum
- exit: zavře terminál

FastOS> """

        self.terminal_text.insert(tk.END, welcome_text)
        self.terminal_text.bind('<Return>', self.process_terminal_command)

    def process_terminal_command(self, event):
        """Zpracuje příkaz v terminálu"""
        # Získá aktuální řádek
        current_line = self.terminal_text.get("end-2l", "end-1c")
        if "FastOS> " in current_line:
            command = current_line.split("FastOS> ")[-1].strip()

            # Zpracování příkazů
            if command == "help":
                response = "\nDostupné příkazy:\n- help, ls, clear, date, sysinfo, games, exit\n"
            elif command == "ls":
                response = "\nSoubory:\n📁 Dokumenty\n📁 Obrázky\n🎮 Light or Dead.exe\n⚙️ config.json\n"
            elif command == "clear":
                self.terminal_text.delete(1.0, tk.END)
                response = "FastOS Terminal v1.0\nFastOS> "
            elif command == "date":
                response = f"\n{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
            elif command == "sysinfo":
                response = "\nFastOS v1.0 - Nejrychlejší OS na světě!\nArchitektura: x64\nJádro: FastKernel\n"
            elif command == "games":
                response = "\nSpouštím herní centrum...\n"
                self.root.after(1000, self.open_games)
            elif command == "exit":
                event.widget.master.destroy()
                return
            else:
                response = f"\nNeznámý příkaz: {command}\nZadejte 'help' pro nápovědu.\n"

            self.terminal_text.insert(tk.END, response + "FastOS> ")
            self.terminal_text.see(tk.END)

    def open_games(self):
        """Otevře herní centrum"""
        games_window = tk.Toplevel(self.root)
        games_window.title("🎮 FastOS Herní centrum")
        games_window.geometry("900x700")
        games_window.configure(bg='#1a1a2e')

        # Hlavička
        header = tk.Label(
            games_window,
            text="🎮 HERNÍ CENTRUM",
            font=('Arial', 24, 'bold'),
            fg='#e94560',
            bg='#1a1a2e'
        )
        header.pack(pady=20)

        # Grid pro hry
        games_frame = tk.Frame(games_window, bg='#1a1a2e')
        games_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # Ukázkové hry
        games = [
            ("🎯 Light or Dead", "Tvoje oblíbená hra!", self.launch_light_or_dead),
            ("🐍 Snake", "Klasická hadí hra", self.launch_snake),
            ("🧩 Puzzle", "Logická hra", self.launch_puzzle),
            ("🏎️ Racing", "Závodní hra", self.launch_racing),
            ("🎲 Casino", "Hazardní hry", self.launch_casino),
            ("🎪 Arcade", "Retro hry", self.launch_arcade)
        ]

        row, col = 0, 0
        for title, desc, command in games:
            game_frame = tk.Frame(games_frame, bg='#16213e', relief='raised', bd=2)
            game_frame.grid(row=row, column=col, padx=10, pady=10, sticky='nsew')

            tk.Label(game_frame, text=title, font=('Arial', 16, 'bold'),
                    fg='white', bg='#16213e').pack(pady=10)
            tk.Label(game_frame, text=desc, font=('Arial', 10),
                    fg='#cccccc', bg='#16213e').pack()
            tk.Button(game_frame, text="HRÁT", command=command,
                     bg='#e94560', fg='white', font=('Arial', 12, 'bold')).pack(pady=10)

            col += 1
            if col > 2:
                col = 0
                row += 1

        # Konfigurace gridu
        for i in range(3):
            games_frame.columnconfigure(i, weight=1)
        for i in range(2):
            games_frame.rowconfigure(i, weight=1)

    # ========== HERNÍ FUNKCE ==========

    def launch_light_or_dead(self):
        """Spustí Light or Dead"""
        messagebox.showinfo("🎯 Light or Dead",
                           "Spouštím tvoji oblíbenou hru Light or Dead!\n\n" +
                           "🚀 Optimalizováno pro FastOS!\n" +
                           "⚡ Nejrychlejší výkon na světě!")

    def launch_snake(self):
        """Spustí Snake hru"""
        snake_window = tk.Toplevel(self.root)
        snake_window.title("🐍 FastOS Snake")
        snake_window.geometry("600x600")
        snake_window.configure(bg='#000000')

        canvas = tk.Canvas(snake_window, bg='#000000', width=580, height=580)
        canvas.pack(padx=10, pady=10)

        # Jednoduchá Snake hra
        canvas.create_rectangle(100, 100, 120, 120, fill='#00ff00', tags='snake')
        canvas.create_rectangle(200, 200, 220, 220, fill='#ff0000', tags='food')

        tk.Label(snake_window, text="Použij šipky pro pohyb!",
                fg='white', bg='#000000', font=('Arial', 14)).pack()

    def launch_puzzle(self):
        """Spustí Puzzle hru"""
        messagebox.showinfo("🧩 Puzzle", "Logická hra se spouští...\n🧠 Trénuj svůj mozek!")

    def launch_racing(self):
        """Spustí Racing hru"""
        messagebox.showinfo("🏎️ Racing", "Závodní hra se spouští...\n🏁 Připrav se na rychlost!")

    def launch_casino(self):
        """Spustí Casino hry"""
        messagebox.showinfo("🎲 Casino", "Hazardní hry se spouštějí...\n💰 Hodně štěstí!")

    def launch_arcade(self):
        """Spustí Arcade hry"""
        messagebox.showinfo("🎪 Arcade", "Retro hry se spouštějí...\n👾 Nostalgická zábava!")

    def open_browser(self):
        """Otevře skutečný web browser"""
        browser_window = tk.Toplevel(self.root)
        browser_window.title("🌐 FastOS Browser - Skutečný Internet!")
        browser_window.geometry("1200x800")
        browser_window.configure(bg='#1a1a2e')

        # Adresní řádek
        address_frame = tk.Frame(browser_window, bg='#16213e', height=50)
        address_frame.pack(fill='x')
        address_frame.pack_propagate(False)

        tk.Button(address_frame, text="⬅️", bg='#0f3460', fg='white',
                 command=self.browser_back).pack(side='left', padx=5, pady=10)
        tk.Button(address_frame, text="➡️", bg='#0f3460', fg='white',
                 command=self.browser_forward).pack(side='left', padx=5, pady=10)
        tk.Button(address_frame, text="🔄", bg='#0f3460', fg='white',
                 command=self.browser_refresh).pack(side='left', padx=5, pady=10)

        self.address_entry = tk.Entry(address_frame, font=('Arial', 12), width=70)
        self.address_entry.pack(side='left', padx=10, pady=10, fill='x', expand=True)
        self.address_entry.insert(0, "google.com")
        self.address_entry.bind('<Return>', self.load_webpage)

        tk.Button(address_frame, text="🔍 Přejít", bg='#e94560', fg='white',
                 font=('Arial', 12, 'bold'), command=self.load_webpage).pack(side='left', padx=5, pady=10)

        # Rychlé odkazy
        quick_frame = tk.Frame(browser_window, bg='#16213e', height=40)
        quick_frame.pack(fill='x')
        quick_frame.pack_propagate(False)

        quick_links = [
            ("🔍 Google", "google.com"),
            ("📺 YouTube", "youtube.com"),
            ("📰 Seznam", "seznam.cz"),
            ("🎮 Steam", "store.steampowered.com"),
            ("💻 GitHub", "github.com"),
            ("🌐 FastOS", "fastos.local")
        ]

        for name, url in quick_links:
            btn = tk.Button(quick_frame, text=name, bg='#0f3460', fg='white',
                           font=('Arial', 9), command=lambda u=url: self.quick_navigate(u))
            btn.pack(side='left', padx=2, pady=5)

        # Obsah stránky
        content_frame = tk.Frame(browser_window, bg='white')
        content_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # Scrollable text pro obsah
        self.page_content = tk.Text(content_frame, font=('Arial', 11), wrap='word',
                                   bg='white', fg='black')
        scrollbar = tk.Scrollbar(content_frame, command=self.page_content.yview)
        self.page_content.config(yscrollcommand=scrollbar.set)

        scrollbar.pack(side='right', fill='y')
        self.page_content.pack(side='left', fill='both', expand=True)

        # Status bar
        self.status_bar = tk.Label(browser_window, text="🚀 FastOS Browser připraven",
                                  bg='#16213e', fg='white', font=('Arial', 10), anchor='w')
        self.status_bar.pack(fill='x')

        # Načte úvodní stránku
        self.load_webpage()

    def quick_navigate(self, url):
        """Rychlá navigace"""
        self.address_entry.delete(0, tk.END)
        self.address_entry.insert(0, url)
        self.load_webpage()

    def browser_back(self):
        """Zpět v prohlížeči"""
        self.status_bar.config(text="⬅️ Zpět (simulace)")

    def browser_forward(self):
        """Vpřed v prohlížeči"""
        self.status_bar.config(text="➡️ Vpřed (simulace)")

    def browser_refresh(self):
        """Obnovit stránku"""
        self.load_webpage()

    def load_webpage(self, event=None):
        """Načte webovou stránku"""
        url = self.address_entry.get().strip()
        if not url:
            return

        # Přidá http:// pokud chybí
        if not url.startswith(('http://', 'https://')):
            if url == "fastos.local":
                url = "fastos.local"
            else:
                url = f"https://{url}"

        self.status_bar.config(text=f"🔄 Načítám {url}...")
        self.page_content.delete(1.0, tk.END)

        try:
            if "fastos.local" in url:
                # Lokální FastOS stránka
                self.load_fastos_page()
            elif "google.com" in url:
                self.load_google_page()
            elif "youtube.com" in url:
                self.load_youtube_page()
            elif "seznam.cz" in url:
                self.load_seznam_page()
            elif "github.com" in url:
                self.load_github_page()
            elif "store.steampowered.com" in url:
                self.load_steam_page()
            else:
                # Pokus o načtení skutečné stránky
                self.load_real_webpage(url)

        except Exception as e:
            self.page_content.insert(tk.END, f"❌ Chyba při načítání stránky: {str(e)}")
            self.status_bar.config(text="❌ Chyba při načítání")

    def load_fastos_page(self):
        """Načte FastOS domovskou stránku"""
        content = """
🚀 FASTOS.LOCAL - NEJRYCHLEJŠÍ OS NA SVĚTĚ! 🚀

═══════════════════════════════════════════════════════════════

📰 AKTUÁLNÍ NOVINKY:
• FastOS v1.0 OFICIÁLNĚ VYDÁN!
• Rekordní rychlost - 0.1s boot time!
• Integrované AI nástroje
• Plná podpora Light or Dead hry
• Skutečný internetový prohlížeč

🎮 HERNÍ SEKCE:
• Light or Dead - OPTIMALIZOVÁNO pro FastOS
• Snake - Klasická hra s FastOS designem
• Herní centrum s 6+ hrami
• Podpora všech Steam her

💻 TECHNICKÉ SPECIFIKACE:
• Architektura: FastKernel x64
• RAM optimalizace: 99% efektivita
• CPU využití: Minimální overhead
• Grafika: Akcelerované rendering

🔧 VÝVOJÁŘSKÉ NÁSTROJE:
• Integrovaný terminál s příkazy
• AI asistent pro programování
• Kalkulačka s pokročilými funkcemi
• Správce úloh v reálném čase

🌟 UNIKÁTNÍ FUNKCE:
• Živé počasí widget
• Hudební přehrávač s FastOS soundtracky
• Matrix efekt s animacemi
• ChatBot s umělou inteligencí

📞 KONTAKT & PODPORA:
Email: <EMAIL>
Discord: FastOS Community
GitHub: github.com/fastos

© 2024 FastOS Corporation - Nejrychlejší OS na světě!
        """
        self.page_content.insert(tk.END, content)
        self.status_bar.config(text="✅ FastOS.local načteno")

    def load_google_page(self):
        """Simuluje Google"""
        content = """
🔍 GOOGLE - VYHLEDÁVÁNÍ

═══════════════════════════════════════════════════════════════

[🔍 Vyhledávací pole]

🔥 POPULÁRNÍ VYHLEDÁVÁNÍ:
• FastOS download
• Light or Dead gameplay
• Python programming
• AI development
• Game development

📰 NOVINKY:
• Nový operační systém FastOS láme rekordy rychlosti
• Light or Dead hra získává popularitu
• AI nástroje revolucionizují vývoj

🌐 UŽITEČNÉ ODKAZY:
• Gmail
• YouTube
• Mapy
• Překladač
• Obrázky

💡 TIP: Zkuste vyhledat "FastOS" pro nejnovější informace!

Poznámka: Toto je simulace Google v FastOS prohlížeči.
Pro skutečný Google použijte externí prohlížeč.
        """
        self.page_content.insert(tk.END, content)
        self.status_bar.config(text="✅ Google.com simulace načtena")

    def load_youtube_page(self):
        """Simuluje YouTube"""
        content = """
📺 YOUTUBE - VIDEA

═══════════════════════════════════════════════════════════════

🔥 DOPORUČENÁ VIDEA:

🎮 [NOVÉ] Light or Dead - Kompletní gameplay
   👁️ 1.2M zhlédnutí • před 2 dny

🚀 [HOT] FastOS - Nejrychlejší OS na světě!
   👁️ 856K zhlédnutí • před 1 týdnem

💻 Python Tutorial - Vytvoř si vlastní OS
   👁️ 234K zhlédnutí • před 3 dny

🎵 FastOS Theme Song - Epic Orchestral
   👁️ 445K zhlédnutí • před 5 dní

🎯 Light or Dead - Boss Fight Compilation
   👁️ 678K zhlédnutí • před 1 týdnem

📱 KATEGORIE:
• Gaming
• Technology
• Programming
• Music
• Education

🔔 ODBĚRY: Přihlaste se k odběru FastOS kanálu!

Poznámka: Toto je simulace YouTube v FastOS prohlížeči.
        """
        self.page_content.insert(tk.END, content)
        self.status_bar.config(text="✅ YouTube.com simulace načtena")

    def load_seznam_page(self):
        """Simuluje Seznam.cz"""
        content = """
📰 SEZNAM.CZ - ČESKÝ PORTÁL

═══════════════════════════════════════════════════════════════

🔍 [Vyhledávání na Seznamu]

📰 HLAVNÍ ZPRÁVY:
• Český vývojář vytvoří nejrychlejší OS na světě
• FastOS láme všechny rekordy rychlosti
• Light or Dead - nová česká hra boduje

🌤️ POČASÍ:
Praha: ☀️ 23°C, slunečno
Brno: ⛅ 21°C, polojasno
Ostrava: 🌧️ 19°C, déšť

📧 SLUŽBY:
• Email
• Mapy
• Firmy
• Reality
• Auto

💰 EKONOMIKA:
• CZK/EUR: 24.85
• CZK/USD: 22.45
• Akcie rostou

🏆 SPORT:
• Fotbal: Liga pokračuje
• Hokej: Playoff začíná
• Tenis: Turnaj v Praze

Poznámka: Simulace Seznam.cz v FastOS prohlížeči.
        """
        self.page_content.insert(tk.END, content)
        self.status_bar.config(text="✅ Seznam.cz simulace načtena")

    def load_github_page(self):
        """Simuluje GitHub"""
        content = """
💻 GITHUB - KÓD PRO VŠECHNY

═══════════════════════════════════════════════════════════════

🔥 TRENDING REPOSITORIES:

⭐ fastos/fastos-core
   🚀 Nejrychlejší OS na světě - Python implementation
   ⭐ 15.2k stars • 🍴 2.1k forks • Python

⭐ lightor-dead/game-engine
   🎮 Light or Dead - Open source game engine
   ⭐ 8.7k stars • 🍴 1.2k forks • Python

⭐ ai-assistant/chatbot
   🤖 AI asistent pro FastOS
   ⭐ 5.4k stars • 🍴 890 forks • Python

⭐ matrix-effect/animation
   🌟 Matrix efekt pro FastOS
   ⭐ 3.2k stars • 🍴 456 forks • JavaScript

📊 VAŠE AKTIVITY:
• 0 contributions dnes
• Založte si účet pro sledování projektů!

🔍 EXPLORE:
• Python projekty
• Game development
• AI & Machine Learning
• Operating Systems

Poznámka: Simulace GitHub v FastOS prohlížeči.
        """
        self.page_content.insert(tk.END, content)
        self.status_bar.config(text="✅ GitHub.com simulace načtena")

    def load_steam_page(self):
        """Simuluje Steam"""
        content = """
🎮 STEAM - HERNÍ PLATFORMA

═══════════════════════════════════════════════════════════════

🔥 DOPORUČENÉ HRY:

🎯 Light or Dead - NOVÁ HRA!
   💰 299 Kč • ⭐⭐⭐⭐⭐ (98% pozitivní)
   🚀 OPTIMALIZOVÁNO PRO FASTOS!

🐍 Snake Remastered
   💰 49 Kč • ⭐⭐⭐⭐ (87% pozitivní)
   🎮 Klasika v novém kabátě

🧩 Puzzle Master
   💰 199 Kč • ⭐⭐⭐⭐ (92% pozitivní)
   🧠 Trénink pro mozek

🏎️ Racing Legends
   💰 599 Kč • ⭐⭐⭐⭐⭐ (95% pozitivní)
   🏁 Nejrealističtější závody

💎 SLEVY TÝDNE:
• Všechny hry kompatibilní s FastOS -50%!
• Light or Dead soundtrack ZDARMA
• FastOS Gaming Bundle za 999 Kč

🏆 ACHIEVEMENTY:
• FastOS Gamer - Hraj na nejrychlejším OS
• Speed Runner - Dokončuj hry rychleji
• Light Master - Ovládni Light or Dead

Poznámka: Simulace Steam v FastOS prohlížeči.
        """
        self.page_content.insert(tk.END, content)
        self.status_bar.config(text="✅ Steam simulace načtena")

    def load_real_webpage(self, url):
        """Pokusí se načíst skutečnou webovou stránku"""
        try:
            # Upozornění uživatele
            self.page_content.insert(tk.END, f"🌐 Pokouším se načíst skutečnou stránku: {url}\n\n")
            self.page_content.insert(tk.END, "⚠️  POZOR: Toto je pokus o načtení skutečné webové stránky.\n")
            self.page_content.insert(tk.END, "FastOS Browser má omezenou funkcionalitu.\n\n")

            # Pokus o načtení (velmi základní)
            import urllib.request
            import urllib.parse

            # Timeout 5 sekund
            response = urllib.request.urlopen(url, timeout=5)
            content = response.read().decode('utf-8', errors='ignore')

            # Extrakce základního textu (velmi jednoduché)
            import re
            # Odstraní HTML tagy
            text_content = re.sub(r'<[^>]+>', '', content)
            # Vezme prvních 2000 znaků
            text_content = text_content[:2000] + "..."

            self.page_content.insert(tk.END, "✅ OBSAH STRÁNKY:\n")
            self.page_content.insert(tk.END, "=" * 50 + "\n")
            self.page_content.insert(tk.END, text_content)

            self.status_bar.config(text=f"✅ {url} načteno (základní obsah)")

        except Exception as e:
            self.page_content.insert(tk.END, f"❌ Nepodařilo se načíst {url}\n")
            self.page_content.insert(tk.END, f"Chyba: {str(e)}\n\n")
            self.page_content.insert(tk.END, "💡 TIP: Zkuste některý z rychlých odkazů nahoře\n")
            self.page_content.insert(tk.END, "nebo použijte externí prohlížeč pro plnou funkcionalitu.")
            self.status_bar.config(text=f"❌ Chyba při načítání {url}")

    def open_settings(self):
        """Otevře nastavení"""
        settings_window = tk.Toplevel(self.root)
        settings_window.title("⚙️ FastOS Nastavení")
        settings_window.geometry("800x600")
        settings_window.configure(bg='#1a1a2e')

        # Boční menu
        sidebar = tk.Frame(settings_window, bg='#16213e', width=200)
        sidebar.pack(side='left', fill='y')
        sidebar.pack_propagate(False)

        settings_categories = [
            "🎨 Vzhled",
            "🔊 Zvuk",
            "🌐 Síť",
            "🔒 Bezpečnost",
            "⚡ Výkon",
            "🎮 Hry",
            "📱 Aplikace",
            "🔧 Systém"
        ]

        for category in settings_categories:
            btn = tk.Button(sidebar, text=category, bg='#0f3460', fg='white',
                           font=('Arial', 12), relief='flat', anchor='w', width=20)
            btn.pack(fill='x', padx=5, pady=2)

        # Hlavní oblast nastavení
        main_settings = tk.Frame(settings_window, bg='#1a1a2e')
        main_settings.pack(side='right', fill='both', expand=True, padx=20, pady=20)

        tk.Label(main_settings, text="⚙️ NASTAVENÍ FASTOS",
                font=('Arial', 20, 'bold'), fg='#e94560', bg='#1a1a2e').pack(pady=20)

        # Ukázkové nastavení
        tk.Label(main_settings, text="🎨 Vzhled a téma:",
                font=('Arial', 14, 'bold'), fg='white', bg='#1a1a2e').pack(anchor='w', pady=10)

        theme_var = tk.StringVar(value="Tmavé")
        themes = ["Tmavé", "Světlé", "Automatické"]
        for theme in themes:
            tk.Radiobutton(main_settings, text=theme, variable=theme_var, value=theme,
                          bg='#1a1a2e', fg='white', selectcolor='#16213e').pack(anchor='w')

        tk.Label(main_settings, text="⚡ Výkonnostní režim:",
                font=('Arial', 14, 'bold'), fg='white', bg='#1a1a2e').pack(anchor='w', pady=(20,10))

        performance_var = tk.BooleanVar(value=True)
        tk.Checkbutton(main_settings, text="Maximální výkon", variable=performance_var,
                      bg='#1a1a2e', fg='white', selectcolor='#16213e').pack(anchor='w')

        tk.Button(main_settings, text="💾 Uložit nastavení", bg='#e94560', fg='white',
                 font=('Arial', 14, 'bold')).pack(pady=30)

    def open_task_manager(self):
        """Otevře správce úloh"""
        task_window = tk.Toplevel(self.root)
        task_window.title("📊 FastOS Správce úloh")
        task_window.geometry("900x600")
        task_window.configure(bg='#1a1a2e')

        # Hlavička
        header = tk.Label(task_window, text="📊 SPRÁVCE ÚLOH",
                         font=('Arial', 20, 'bold'), fg='#e94560', bg='#1a1a2e')
        header.pack(pady=10)

        # Tabulka procesů
        columns = ('Proces', 'PID', 'CPU %', 'Paměť', 'Status')
        tree = ttk.Treeview(task_window, columns=columns, show='headings', height=15)

        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=150)

        # Ukázkové procesy
        processes = [
            ("FastOS Kernel", "1", "2.1", "128 MB", "Běží"),
            ("Desktop Manager", "42", "1.5", "64 MB", "Běží"),
            ("File Manager", "156", "0.8", "32 MB", "Běží"),
            ("Light or Dead", "789", "15.2", "512 MB", "Běží"),
            ("Web Browser", "234", "3.4", "256 MB", "Běží"),
            ("Terminal", "567", "0.2", "16 MB", "Běží"),
            ("System Monitor", "890", "1.1", "24 MB", "Běží")
        ]

        for process in processes:
            tree.insert('', tk.END, values=process)

        tree.pack(fill='both', expand=True, padx=20, pady=10)

        # Tlačítka
        button_frame = tk.Frame(task_window, bg='#1a1a2e')
        button_frame.pack(pady=10)

        tk.Button(button_frame, text="🔄 Obnovit", bg='#0f3460', fg='white',
                 font=('Arial', 12)).pack(side='left', padx=10)
        tk.Button(button_frame, text="❌ Ukončit proces", bg='#e94560', fg='white',
                 font=('Arial', 12)).pack(side='left', padx=10)

    def open_system_tools(self):
        """Otevře systémové nástroje"""
        tools_window = tk.Toplevel(self.root)
        tools_window.title("🔧 FastOS Systémové nástroje")
        tools_window.geometry("700x500")
        tools_window.configure(bg='#1a1a2e')

        # Hlavička
        header = tk.Label(tools_window, text="🔧 SYSTÉMOVÉ NÁSTROJE",
                         font=('Arial', 20, 'bold'), fg='#e94560', bg='#1a1a2e')
        header.pack(pady=20)

        # Grid nástrojů
        tools_frame = tk.Frame(tools_window, bg='#1a1a2e')
        tools_frame.pack(fill='both', expand=True, padx=20, pady=20)

        tools = [
            ("🧹 Vyčištění disku", "Uvolní místo na disku"),
            ("🔍 Defragmentace", "Optimalizuje výkon disku"),
            ("🛡️ Antivirus", "Kontrola bezpečnosti"),
            ("📊 Diagnostika", "Systémová diagnostika"),
            ("🔧 Registry", "Editor registrů"),
            ("📝 Logy", "Systémové protokoly")
        ]

        row, col = 0, 0
        for title, desc in tools:
            tool_frame = tk.Frame(tools_frame, bg='#16213e', relief='raised', bd=2)
            tool_frame.grid(row=row, column=col, padx=10, pady=10, sticky='nsew')

            tk.Label(tool_frame, text=title, font=('Arial', 14, 'bold'),
                    fg='white', bg='#16213e').pack(pady=10)
            tk.Label(tool_frame, text=desc, font=('Arial', 10),
                    fg='#cccccc', bg='#16213e').pack()
            tk.Button(tool_frame, text="SPUSTIT",
                     bg='#e94560', fg='white', font=('Arial', 10, 'bold')).pack(pady=10)

            col += 1
            if col > 1:
                col = 0
                row += 1

        # Konfigurace gridu
        for i in range(2):
            tools_frame.columnconfigure(i, weight=1)
        for i in range(3):
            tools_frame.rowconfigure(i, weight=1)

    def shutdown_system(self):
        """Vypne systém"""
        result = messagebox.askyesno("❌ Vypnutí FastOS",
                                   "Opravdu chceš vypnout FastOS?\n\n" +
                                   "🚀 Nejrychlejší OS na světě se vypne...")
        if result:
            # Animace vypínání
            shutdown_window = tk.Toplevel(self.root)
            shutdown_window.title("Vypínání...")
            shutdown_window.geometry("400x200")
            shutdown_window.configure(bg='#000000')

            tk.Label(shutdown_window, text="FastOS se vypína...",
                    font=('Arial', 18, 'bold'), fg='white', bg='#000000').pack(expand=True)

            # Zavře po 2 sekundách
            self.root.after(2000, self.root.quit)

    # ========== NOVÉ LUXUSNÍ APLIKACE ==========

    def open_ai_painter(self):
        """Otevře AI malíř"""
        painter_window = tk.Toplevel(self.root)
        painter_window.title("🎨 FastOS AI Malíř")
        painter_window.geometry("900x700")
        painter_window.configure(bg='#1a1a2e')

        # Hlavička
        header = tk.Label(painter_window, text="🎨 AI MALÍŘ",
                         font=('Arial', 24, 'bold'), fg='#e94560', bg='#1a1a2e')
        header.pack(pady=10)

        # Toolbar
        toolbar = tk.Frame(painter_window, bg='#16213e', height=60)
        toolbar.pack(fill='x', padx=10, pady=5)
        toolbar.pack_propagate(False)

        # Nástroje
        tools = ["🖌️ Štětec", "✏️ Tužka", "🌈 Barvy", "🔄 AI Generátor", "💾 Uložit"]
        for tool in tools:
            btn = tk.Button(toolbar, text=tool, bg='#0f3460', fg='white',
                           font=('Arial', 10, 'bold'))
            btn.pack(side='left', padx=5, pady=10)

        # Plátno
        canvas_frame = tk.Frame(painter_window, bg='#1a1a2e')
        canvas_frame.pack(fill='both', expand=True, padx=10, pady=10)

        canvas = tk.Canvas(canvas_frame, bg='white', width=850, height=500)
        canvas.pack()

        # Předkreslené AI umění
        canvas.create_rectangle(100, 100, 300, 200, fill='#ff6b6b', outline='#4ecdc4', width=3)
        canvas.create_oval(400, 150, 600, 350, fill='#45b7d1', outline='#96ceb4', width=3)
        canvas.create_polygon(650, 100, 750, 200, 700, 300, fill='#feca57', outline='#ff9ff3', width=3)

        canvas.create_text(425, 450, text="🤖 AI Vygenerované umění pro FastOS!",
                          font=('Arial', 16, 'bold'), fill='#e94560')

    def open_chatbot(self):
        """Otevře ChatBot"""
        chat_window = tk.Toplevel(self.root)
        chat_window.title("🤖 FastOS ChatBot")
        chat_window.geometry("600x500")
        chat_window.configure(bg='#1a1a2e')

        # Hlavička
        header = tk.Label(chat_window, text="🤖 FASTOS CHATBOT",
                         font=('Arial', 18, 'bold'), fg='#e94560', bg='#1a1a2e')
        header.pack(pady=10)

        # Chat oblast
        chat_frame = tk.Frame(chat_window, bg='#1a1a2e')
        chat_frame.pack(fill='both', expand=True, padx=20, pady=10)

        self.chat_text = tk.Text(chat_frame, bg='#0f3460', fg='white',
                                font=('Arial', 11), wrap='word', height=20)
        self.chat_text.pack(fill='both', expand=True)

        # Úvodní zprávy
        welcome_chat = """🤖 FastOS Bot: Ahoj! Jsem FastOS AI asistent!
Jak ti mohu pomoci?

Můžu ti:
• Optimalizovat Light or Dead
• Vysvětlit funkce FastOS
• Pomoci s programováním
• Bavit se o hrách
• Dát tipy na zlepšení výkonu

Napiš mi cokoliv! 😊

---
"""
        self.chat_text.insert(tk.END, welcome_chat)

        # Input oblast
        input_frame = tk.Frame(chat_window, bg='#1a1a2e')
        input_frame.pack(fill='x', padx=20, pady=10)

        self.chat_input = tk.Entry(input_frame, bg='#16213e', fg='white',
                                  font=('Arial', 12), width=40)
        self.chat_input.pack(side='left', fill='x', expand=True, padx=(0, 10))
        self.chat_input.bind('<Return>', self.send_chat_message)

        send_btn = tk.Button(input_frame, text="📤 Odeslat", bg='#e94560', fg='white',
                            font=('Arial', 12, 'bold'), command=self.send_chat_message)
        send_btn.pack(side='right')

    def send_chat_message(self, event=None):
        """Odešle zprávu do chatu"""
        message = self.chat_input.get().strip()
        if not message:
            return

        # Přidá uživatelovu zprávu
        self.chat_text.insert(tk.END, f"👤 Ty: {message}\n\n")
        self.chat_input.delete(0, tk.END)

        # AI odpověď
        responses = {
            "ahoj": "🤖 Ahoj! Jak se máš? Baví tě FastOS?",
            "light or dead": "🎮 Light or Dead je skvělá hra! FastOS ji optimalizuje pro maximální výkon!",
            "jak": "🤖 Skvěle! FastOS běží na plné obrátky!",
            "pomoc": "🆘 Samozřejmě! S čím potřebuješ pomoct?",
            "hry": "🎮 V herním centru máš spoustu her! Zkus Snake nebo spusť Light or Dead!",
            "rychlost": "⚡ FastOS je nejrychlejší OS na světě! Optimalizován pro maximální výkon!",
            "default": "🤖 To je zajímavé! Řekni mi víc o tom, co tě zajímá!"
        }

        # Najde odpověď
        response = responses.get("default")
        for key in responses:
            if key in message.lower():
                response = responses[key]
                break

        # Přidá AI odpověď
        self.chat_text.insert(tk.END, f"🤖 FastOS Bot: {response}\n\n")
        self.chat_text.see(tk.END)

    def open_music_center(self):
        """Otevře hudební centrum"""
        music_window = tk.Toplevel(self.root)
        music_window.title("🎵 FastOS Hudební centrum")
        music_window.geometry("800x600")
        music_window.configure(bg='#1a1a2e')

        # Hlavička
        header = tk.Label(music_window, text="🎵 HUDEBNÍ CENTRUM",
                         font=('Arial', 20, 'bold'), fg='#e94560', bg='#1a1a2e')
        header.pack(pady=15)

        # Playlist
        playlist_frame = tk.Frame(music_window, bg='#16213e')
        playlist_frame.pack(fill='both', expand=True, padx=20, pady=10)

        tk.Label(playlist_frame, text="🎶 PLAYLIST",
                font=('Arial', 14, 'bold'), fg='white', bg='#16213e').pack(pady=10)

        songs = [
            "🎵 FastOS Theme - Epic Orchestral",
            "🎮 Light or Dead Soundtrack - Action",
            "⚡ Speed Boost - Electronic",
            "🚀 System Startup - Ambient",
            "🌟 Matrix Code - Synthwave",
            "🎯 Gaming Zone - Rock",
            "💻 Terminal Beats - Chiptune",
            "🌈 AI Dreams - Experimental"
        ]

        song_listbox = tk.Listbox(playlist_frame, bg='#0f3460', fg='white',
                                 font=('Arial', 12), height=12)
        song_listbox.pack(fill='both', expand=True, padx=10, pady=10)

        for song in songs:
            song_listbox.insert(tk.END, song)

        # Ovládání
        controls_frame = tk.Frame(music_window, bg='#1a1a2e')
        controls_frame.pack(pady=15)

        control_buttons = ["⏮️ Předchozí", "⏸️ Pauza", "▶️ Přehrát", "⏭️ Další", "🔀 Náhodně"]
        for btn_text in control_buttons:
            btn = tk.Button(controls_frame, text=btn_text, bg='#e94560', fg='white',
                           font=('Arial', 12, 'bold'), width=12)
            btn.pack(side='left', padx=5)

    def open_matrix_effect(self):
        """Otevře Matrix efekt"""
        matrix_window = tk.Toplevel(self.root)
        matrix_window.title("🌟 FastOS Matrix")
        matrix_window.geometry("800x600")
        matrix_window.configure(bg='#000000')

        # Canvas pro Matrix efekt
        matrix_canvas = tk.Canvas(matrix_window, bg='#000000', width=780, height=580)
        matrix_canvas.pack(padx=10, pady=10)

        # Simulace Matrix efektu
        import random
        for i in range(50):
            x = random.randint(10, 770)
            y = random.randint(10, 570)
            char = random.choice("01FastOS🚀⚡💻🎮")
            color = random.choice(["#00ff00", "#00aa00", "#ffffff", "#e94560"])
            matrix_canvas.create_text(x, y, text=char, fill=color,
                                    font=('Courier', random.randint(8, 16), 'bold'))

        # Hlavní text
        matrix_canvas.create_text(400, 300, text="FASTOS MATRIX",
                                fill='#00ff00', font=('Courier', 24, 'bold'))
        matrix_canvas.create_text(400, 350, text="🚀 NEJRYCHLEJŠÍ OS NA SVĚTĚ 🚀",
                                fill='#e94560', font=('Arial', 16, 'bold'))

        # Animace (jednoduchá)
        self.animate_matrix(matrix_canvas)

    def animate_matrix(self, canvas):
        """Animuje Matrix efekt"""
        import random
        # Přidá nové znaky
        for i in range(5):
            x = random.randint(10, 770)
            y = random.randint(10, 570)
            char = random.choice("01FastOS🚀⚡💻🎮")
            color = random.choice(["#00ff00", "#00aa00", "#ffffff", "#e94560"])
            canvas.create_text(x, y, text=char, fill=color,
                             font=('Courier', random.randint(8, 16), 'bold'))

        # Pokračuje v animaci
        try:
            self.root.after(500, lambda: self.animate_matrix(canvas))
        except:
            pass  # Okno bylo zavřeno

    def run(self):
        """Spustí OS emulátor"""
        self.root.mainloop()

# Spuštění FastOS
if __name__ == "__main__":
    print("🚀 Spouštím FastOS - Nejrychlejší OS na světě!")
    print("⚡ Optimalizováno pro maximální výkon!")
    print("🎮 Speciálně navrženo pro Light or Dead!")
    print("-" * 50)

    os_system = FastOS()
    os_system.run()
